<?php

declare(strict_types=1);

namespace App\Services\AI;

use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use JsonException;

class TemplateGenerator
{
    private readonly string $token;

    private readonly string $url;

    private readonly string $model;

    public function __construct(
        public string $templateType,
        public string $description,
        public string $language = 'ar'
    ) {
        $this->token = Config::string('message_check.token');
        $this->url = Config::string('message_check.url');
        $this->model = Config::string('message_check.model');
    }

    /**
     * Generate a message template based on the provided parameters
     *
     * @throws GuzzleException
     */
    public function generate(): array
    {
        $prompt = $this->buildPrompt();

        $client = $this->getClient();
        $url = "$this->url$this->model:generateContent?key=$this->token";

        try {
            $body = json_encode([
                'contents' => [
                    ['parts' => [['text' => $prompt]]],
                ],
            ], JSON_THROW_ON_ERROR);

            $response = $client->post($url, [
                'headers' => ['Content-Type' => 'application/json'],
                'body' => $body,
                'http_errors' => false,
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();

            if ($statusCode !== 200) {
                Log::error('TemplateGenerator: API request failed', [
                    'status' => $statusCode,
                    'response' => $responseBody,
                ]);

                return [
                    'success' => false,
                    'error' => 'API request failed',
                    'content' => '',
                    'parameters' => [],
                ];
            }

            $data = json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);
            /** @var array{candidates: array<int, array{content: array{parts: array<int, array{text: string|null}>}}>} $data */
            $text = $data['candidates'][0]['content']['parts'][0]['text'] ?? null;

            if (! $text) {
                Log::error('TemplateGenerator: Missing text in API response', [
                    'response' => $responseBody,
                ]);

                return [
                    'success' => false,
                    'error' => 'Missing text in response',
                    'content' => '',
                    'parameters' => [],
                ];
            }

            return $this->parseResponse($text);

        } catch (RequestException $e) {
            Log::error('TemplateGenerator: Request failed', [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Request failed: '.$e->getMessage(),
                'content' => '',
                'parameters' => [],
            ];
        } catch (JsonException $e) {
            Log::error('TemplateGenerator: JSON parsing failed', [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'JSON parsing failed: '.$e->getMessage(),
                'content' => '',
                'parameters' => [],
            ];
        }
    }

    /**
     * Get the HTTP client instance.
     * This method is primarily used for testing to allow mocking the client.
     */
    protected function getClient(): Client
    {
        return new Client();
    }

    /**
     * Build the prompt for template generation
     */
    private function buildPrompt(): string
    {
        $languageInstruction = $this->language === 'ar'
            ? 'Generate the template in Arabic language.'
            : 'Generate the template in English language.';

        $templateExamples = $this->getTemplateExamples();

        return "You are an SMS template generator. Your task is to create professional SMS message templates.

{$languageInstruction}

Template Type: {$this->templateType}
Description: {$this->description}

Requirements:
1. Keep the message concise and professional (SMS should be under 160 characters when possible)
2. Use parameters in double curly braces for dynamic content (e.g., {{name}}, {{code}}, {{amount}})
3. Make it appropriate for business communication
4. Follow SMS best practices
5. Be culturally appropriate for Libyan audience

Examples of good templates:
{$templateExamples}

Please respond with ONLY a JSON object in this exact format:
{
    \"content\": \"Your generated SMS template here with {{parameters}}\",
    \"parameters\": [
        {\"name\": \"parameter_name\", \"description\": \"What this parameter represents\"}
    ]
}

Do not include any explanation or additional text outside the JSON.";
    }

    /**
     * Get template examples based on type
     */
    private function getTemplateExamples(): string
    {
        $examples = [
            'welcome' => [
                'ar' => 'مرحباً {{name}}! نرحب بك في {{company_name}}. نحن سعداء لانضمامك إلينا.',
                'en' => 'Welcome {{name}}! Thank you for joining {{company_name}}. We\'re excited to have you with us.',
            ],
            'verification' => [
                'ar' => 'رمز التحقق الخاص بك هو: {{code}}. لا تشارك هذا الرمز مع أحد.',
                'en' => 'Your verification code is: {{code}}. Do not share this code with anyone.',
            ],
            'notification' => [
                'ar' => 'عزيزي {{name}}، تم {{action}} بنجاح. شكراً لاستخدام خدماتنا.',
                'en' => 'Dear {{name}}, your {{action}} has been completed successfully. Thank you for using our services.',
            ],
            'reminder' => [
                'ar' => 'تذكير: لديك {{event}} في {{date}} الساعة {{time}}.',
                'en' => 'Reminder: You have {{event}} on {{date}} at {{time}}.',
            ],
        ];

        $example = $examples[$this->templateType][$this->language] ?? $examples['notification'][$this->language];

        return "- {$example}";
    }

    /**
     * Parse the AI response and extract content and parameters
     *
     * @return array{success: bool, error: string|null, content: string, parameters: array<int, array{name: string, description: string}>}
     */
    private function parseResponse(string $response): array
    {
        try {
            // Clean the response - remove any Markdown formatting or extra text
            $cleanResponse = trim($response);
            $cleanResponse = preg_replace('/^```json\s*/', '', $cleanResponse);
            $cleanResponse = preg_replace('/\s*```$/', '', $cleanResponse);

            $data = json_decode($cleanResponse, true, 512, JSON_THROW_ON_ERROR);

            if (! isset($data['content'])) {
                return [
                    'success' => false,
                    'error' => 'Invalid response format',
                    'content' => '',
                    'parameters' => [],
                ];
            }

            // Extract parameters from content if not provided
            $parameters = $data['parameters'] ?? [];
            if (empty($parameters)) {
                preg_match_all('/{{(.*?)}}/', $data['content'], $matches);
                $parameters = [];
                foreach ($matches[1] as $match) {
                    $parameters[] = [
                        'name' => trim($match),
                        'description' => 'Dynamic parameter: '.trim($match),
                    ];
                }
            }

            return [
                'success' => true,
                'content' => $data['content'],
                'parameters' => $parameters,
                'error' => null,
            ];

        } catch (JsonException $e) {
            Log::error('TemplateGenerator: Failed to parse AI response', [
                'response' => $response,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Failed to parse response',
                'content' => '',
                'parameters' => [],
            ];
        }
    }
}
